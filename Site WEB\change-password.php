<?php
session_start();
require_once(__DIR__ . '/lib/user.php');
require_once(__DIR__ . '/lib/password_policy.php');

// Vérifier l'authentification
if (!isset($_SESSION['user'])) {
    header('Location: /auth/login.php');
    exit;
}

$success = '';
$error = '';

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $currentPassword = $_POST['current_password'] ?? '';
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    try {
        // Vérifications de base
        if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
            throw new Exception('Tous les champs sont obligatoires');
        }
        
        if ($newPassword !== $confirmPassword) {
            throw new Exception('Les nouveaux mots de passe ne correspondent pas');
        }
        
        if ($currentPassword === $newPassword) {
            throw new Exception('Le nouveau mot de passe doit être différent de l\'ancien');
        }
        
        // Changer le mot de passe
        if (User::changePassword($_SESSION['user']['id'], $currentPassword, $newPassword)) {
            $success = 'Mot de passe modifié avec succès';
            // Vider les champs après succès
            $_POST = [];
        } else {
            $error = 'Erreur lors de la modification du mot de passe';
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Récupérer les exigences de mot de passe
$requirements = PasswordPolicy::getRequirements();
$requirementsText = PasswordPolicy::getRequirementsText();

ob_start();
?>

<div class="mb-4 flex justify-between items-center">
    <h2 class="text-2xl font-bold dark:text-white">Changer mon Mot de Passe</h2>
    <div class="flex space-x-2">
        <a href="/index.php" 
           class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
            Retour à l'Accueil
        </a>
    </div>
</div>

<?php if ($success): ?>
    <div id="alert-success"
         class="flex items-center p-4 mb-4 text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400"
         role="alert">
        <svg class="flex-shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
             viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
        </svg>
        <div class="ms-3 text-sm font-medium"><?php echo htmlspecialchars($success); ?></div>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div id="alert-error"
         class="flex items-center p-4 mb-4 text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400"
         role="alert">
        <svg class="flex-shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
             viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
        </svg>
        <div class="ms-3 text-sm font-medium"><?php echo htmlspecialchars($error); ?></div>
    </div>
<?php endif; ?>

<!-- Exigences de mot de passe -->
<div class="bg-blue-50 dark:bg-gray-800 border border-blue-200 dark:border-blue-800 rounded-lg p-6 mb-6">
    <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-300 mb-4">
        <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
        </svg>
        Exigences pour le Nouveau Mot de Passe
    </h3>
    <ul class="space-y-1 text-sm text-blue-800 dark:text-blue-300">
        <?php foreach ($requirementsText as $requirement): ?>
            <li class="flex items-start">
                <svg class="w-4 h-4 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <?php echo htmlspecialchars($requirement); ?>
            </li>
        <?php endforeach; ?>
    </ul>
</div>

<!-- Formulaire de changement de mot de passe -->
<div class="bg-white dark:bg-gray-800 shadow-md sm:rounded-lg p-6">
    <form method="POST" id="change-password-form">
        <div class="grid grid-cols-1 gap-6">
            <!-- Mot de passe actuel -->
            <div>
                <label for="current_password" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                    Mot de passe actuel <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <input type="password" id="current_password" name="current_password"
                           class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 pr-10 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                           placeholder="Saisissez votre mot de passe actuel"
                           required>
                    <button type="button" onclick="togglePasswordVisibility('current_password')" 
                            class="absolute inset-y-0 right-0 flex items-center pr-3">
                        <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Nouveau mot de passe -->
            <div>
                <label for="new_password" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                    Nouveau mot de passe <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <input type="password" id="new_password" name="new_password"
                           class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 pr-10 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                           placeholder="Saisissez votre nouveau mot de passe"
                           oninput="validateNewPassword()"
                           required>
                    <button type="button" onclick="togglePasswordVisibility('new_password')" 
                            class="absolute inset-y-0 right-0 flex items-center pr-3">
                        <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                        </svg>
                    </button>
                </div>
                <div id="new-password-strength" class="mt-2"></div>
                <div id="new-password-errors" class="mt-2"></div>
                
                <button type="button" onclick="generateSecurePassword('new_password')" 
                        class="mt-2 text-sm text-blue-600 dark:text-blue-400 hover:underline">
                    Générer un mot de passe sécurisé
                </button>
            </div>

            <!-- Confirmation du nouveau mot de passe -->
            <div>
                <label for="confirm_password" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                    Confirmer le nouveau mot de passe <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <input type="password" id="confirm_password" name="confirm_password"
                           class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 pr-10 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                           placeholder="Confirmez votre nouveau mot de passe"
                           oninput="validatePasswordMatch()"
                           required>
                    <button type="button" onclick="togglePasswordVisibility('confirm_password')" 
                            class="absolute inset-y-0 right-0 flex items-center pr-3">
                        <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                        </svg>
                    </button>
                </div>
                <div id="confirm-password-match" class="mt-2"></div>
            </div>
        </div>

        <!-- Boutons d'action -->
        <div class="flex justify-end space-x-3 mt-6">
            <a href="/index.php"
               class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                Annuler
            </a>
            <button type="submit" id="submit-btn"
                    class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                Changer le Mot de Passe
            </button>
        </div>
    </form>
</div>

<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>

<script>
// Configuration des exigences (côté client)
const passwordRequirements = <?php echo json_encode($requirements); ?>;

// Basculer la visibilité du mot de passe
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
    input.setAttribute('type', type);
}

// Valider le nouveau mot de passe
function validateNewPassword() {
    const password = document.getElementById('new_password').value;
    const strengthElement = document.getElementById('new-password-strength');
    const errorsElement = document.getElementById('new-password-errors');

    if (!password) {
        strengthElement.innerHTML = '';
        errorsElement.innerHTML = '';
        return;
    }

    const validation = validatePasswordClient(password);

    // Afficher la force du mot de passe
    const strengthColor = getStrengthColor(validation.strength);
    const strengthText = getStrengthText(validation.strength);
    strengthElement.innerHTML = `<div class="flex justify-between items-center">
        <span class="text-sm font-medium">Force :</span>
        <span class="${strengthColor} text-sm font-medium">${strengthText} (${validation.score}/100)</span>
    </div>`;

    // Afficher les erreurs
    if (validation.errors.length > 0) {
        errorsElement.innerHTML = `<div class="text-sm text-red-600 dark:text-red-400">
            <div class="font-medium mb-1">Problèmes :</div>
            <ul class="list-disc list-inside space-y-1">
                ${validation.errors.map(error => `<li>${error}</li>`).join('')}
            </ul>
        </div>`;
    } else {
        errorsElement.innerHTML = `<div class="flex items-center text-green-600 dark:text-green-400 text-sm">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
            </svg>Mot de passe conforme
        </div>`;
    }

    validatePasswordMatch();
}

// Valider la correspondance des mots de passe
function validatePasswordMatch() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const matchElement = document.getElementById('confirm-password-match');

    if (!confirmPassword) {
        matchElement.innerHTML = '';
        return;
    }

    if (newPassword === confirmPassword) {
        matchElement.innerHTML = `<div class="flex items-center text-green-600 dark:text-green-400 text-sm">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
            </svg>Les mots de passe correspondent
        </div>`;
    } else {
        matchElement.innerHTML = `<div class="flex items-center text-red-600 dark:text-red-400 text-sm">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
            </svg>Les mots de passe ne correspondent pas
        </div>`;
    }
}

// Générer un mot de passe sécurisé
function generateSecurePassword(inputId) {
    const password = generatePassword();
    document.getElementById(inputId).value = password;
    validateNewPassword();

    // Copier dans le presse-papiers si possible
    if (navigator.clipboard) {
        navigator.clipboard.writeText(password).then(() => {
            alert('Mot de passe généré et copié dans le presse-papiers !');
        });
    } else {
        alert('Mot de passe généré ! Pensez à le copier.');
    }
}

// Fonctions de validation (répliques de password-policy.php)
function validatePasswordClient(password) {
    const result = { valid: true, errors: [], score: 0, strength: 'weak' };

    if (password.length < passwordRequirements.min_length) {
        result.valid = false;
        result.errors.push(`Au moins ${passwordRequirements.min_length} caractères`);
    } else { result.score += 20; }

    if (passwordRequirements.require_uppercase && !/[A-Z]/.test(password)) {
        result.valid = false;
        result.errors.push('Au moins une lettre majuscule');
    } else if (passwordRequirements.require_uppercase) { result.score += 15; }

    if (passwordRequirements.require_lowercase && !/[a-z]/.test(password)) {
        result.valid = false;
        result.errors.push('Au moins une lettre minuscule');
    } else if (passwordRequirements.require_lowercase) { result.score += 15; }

    if (passwordRequirements.require_numbers && !/[0-9]/.test(password)) {
        result.valid = false;
        result.errors.push('Au moins un chiffre');
    } else if (passwordRequirements.require_numbers) { result.score += 15; }

    if (passwordRequirements.require_special_chars) {
        const specialChars = (password.match(/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/g) || []).length;
        if (specialChars < passwordRequirements.min_special_chars) {
            result.valid = false;
            result.errors.push(`Au moins ${passwordRequirements.min_special_chars} caractère(s) spécial(aux)`);
        } else { result.score += 20; }
    }

    for (const pattern of passwordRequirements.forbidden_patterns) {
        if (password.toLowerCase().includes(pattern.toLowerCase())) {
            result.valid = false;
            result.errors.push(`Ne doit pas contenir "${pattern}"`);
            break;
        }
    }

    if (hasConsecutiveChars(password, passwordRequirements.max_consecutive_chars)) {
        result.valid = false;
        result.errors.push(`Pas plus de ${passwordRequirements.max_consecutive_chars} caractères identiques consécutifs`);
    }

    if (password.length >= 12) result.score += 10;
    if (password.length >= 16) result.score += 5;

    if (result.score >= 80) result.strength = 'very_strong';
    else if (result.score >= 60) result.strength = 'strong';
    else if (result.score >= 40) result.strength = 'medium';
    else if (result.score >= 20) result.strength = 'weak';
    else result.strength = 'very_weak';

    return result;
}

function hasConsecutiveChars(password, maxConsecutive) {
    let count = 1;
    for (let i = 1; i < password.length; i++) {
        if (password[i] === password[i - 1]) {
            count++;
            if (count > maxConsecutive) return true;
        } else { count = 1; }
    }
    return false;
}

function getStrengthColor(strength) {
    switch (strength) {
        case 'very_strong': return 'text-green-600 dark:text-green-400';
        case 'strong': return 'text-green-500 dark:text-green-300';
        case 'medium': return 'text-yellow-500 dark:text-yellow-400';
        case 'weak': return 'text-orange-500 dark:text-orange-400';
        default: return 'text-red-600 dark:text-red-400';
    }
}

function getStrengthText(strength) {
    switch (strength) {
        case 'very_strong': return 'Très fort';
        case 'strong': return 'Fort';
        case 'medium': return 'Moyen';
        case 'weak': return 'Faible';
        default: return 'Très faible';
    }
}

function generatePassword() {
    let chars = '';
    if (passwordRequirements.require_lowercase) chars += 'abcdefghijklmnopqrstuvwxyz';
    if (passwordRequirements.require_uppercase) chars += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    if (passwordRequirements.require_numbers) chars += '0123456789';
    if (passwordRequirements.require_special_chars) chars += '!@#$%^&*()_+-=[]{}|;:,.<>?';

    if (!chars) chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

    let password = '';
    const length = Math.max(passwordRequirements.min_length, 12);

    if (passwordRequirements.require_lowercase) password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)];
    if (passwordRequirements.require_uppercase) password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)];
    if (passwordRequirements.require_numbers) password += '0123456789'[Math.floor(Math.random() * 10)];
    if (passwordRequirements.require_special_chars) password += '!@#$%^&*'[Math.floor(Math.random() * 8)];

    for (let i = password.length; i < length; i++) {
        password += chars[Math.floor(Math.random() * chars.length)];
    }

    return password.split('').sort(() => Math.random() - 0.5).join('');
}

// Validation du formulaire avant soumission
document.getElementById('change-password-form').addEventListener('submit', function(e) {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;

    if (newPassword !== confirmPassword) {
        e.preventDefault();
        alert('Les nouveaux mots de passe ne correspondent pas');
        return false;
    }

    const validation = validatePasswordClient(newPassword);
    if (!validation.valid) {
        e.preventDefault();
        alert('Le nouveau mot de passe ne respecte pas les exigences de sécurité');
        return false;
    }
});
</script>
